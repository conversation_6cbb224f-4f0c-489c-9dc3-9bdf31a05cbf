<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { Descriptions, Input } from 'ant-design-vue';
  import { ref } from 'vue';
  import { smsFollowupVisitReminder } from '/@/api/case-management';

  // 定义患者信息接口
  interface PatientInfo {
    patientName: string;
    sexName: string;
    age: string;
    profileType: string;
    phone: string;
  }

  const emit = defineEmits(['register', 'success']);

  // 患者信息
  const patientInfo = ref<PatientInfo>({
    patientName: '',
    sexName: '',
    age: '',
    profileType: '',
    phone: '',
  });

  // 短信内容（固定内容）
  const smsContent = ref(
    '亲爱的病友您好，温馨提醒您下周为复诊时间，请您准时到我院完成复查。若您无法按时前来，可与个案管理员联系协调。感谢您的配合，祝您早日康复！',
  );

  // 注册模态框
  const [register, { closeModal }] = useModalInner((data: PatientInfo) => {
    // 接收传入的患者信息
    patientInfo.value = {
      patientName: data.patientName || '',
      sexName: data.sexName || '',
      age: data.age || '',
      profileType: data.profileType || '',
      phone: data.phone || '',
    };
  });

  // 发送短信提醒
  const { loading, runAsync: sendSmsReminder } = useRequest(smsFollowupVisitReminder, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });

  // 处理确认发送
  async function handleOk() {
    if (!patientInfo.value.phone) {
      return;
    }
    await sendSmsReminder(patientInfo.value.phone);
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="复诊提醒"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    :min-height="80"
    width="800px"
    ok-text="发送"
    cancel-text="取消"
    @register="register"
    @ok="handleOk"
    centered
  >
    <!-- 患者基本信息展示 -->
    <Descriptions
      class="mb-4 px-6"
      :column="24"
      :label-style="{ color: '#B0B1B4' }"
      :content-style="{ color: '#252931' }"
    >
      <Descriptions.Item :span="6" label="患者姓名">{{
        patientInfo.patientName
      }}</Descriptions.Item>
      <Descriptions.Item :span="6" label="性别">{{ patientInfo.sexName }}</Descriptions.Item>
      <Descriptions.Item :span="6" label="年龄">{{ patientInfo.age }}</Descriptions.Item>
      <Descriptions.Item :span="6" label="个案类型">{{
        patientInfo.profileType
      }}</Descriptions.Item>
    </Descriptions>

    <!-- 短信内容 -->
    <div class="px-6">
      <div class="mb-2">
        <span style="color: #ff4d4f">*</span>
        <span style="color: #b0b1b4">短信内容：</span>
      </div>
      <Input.TextArea
        v-model:value="smsContent"
        :rows="6"
        :disabled="true"
        style="background-color: #f5f5f5"
      />
    </div>
  </BasicModal>
</template>

<style lang="less" scoped></style>
