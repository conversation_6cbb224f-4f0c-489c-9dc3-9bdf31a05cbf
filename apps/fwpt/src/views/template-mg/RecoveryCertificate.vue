<script setup lang="ts">
  import dayjs from 'dayjs';
  import { computed, ref } from 'vue';
  const props = defineProps<{
    orgUrl?: string;
    name?: any;
    patientName?: any;
    sex?: any;
    age?: any;
    deptName?: any;
    outpatientNo?: any;
    contactPhone?: any;
    diagnoseIllness?: string;
    treatDate?: any;
    diagnoseDate?: any;
    orgName?: string;
    departmentName?: string;
    diagnoseDocSign?: string;
    deptDirectorSign?: string;
    deptSign?: string;
    doctorSign?: string;
    signature?: string;
    isPass?: boolean;
    issueDate?: string;
  }>();
  const issueDateStr = computed(() => dayjs(props.issueDate).format('YYYY年MM月DD日'));
  const effectiveDateStr = computed(() =>
    dayjs(props.issueDate).add(30, 'day').format('YYYY-MM-DD'),
  );
  const doctorSignUrl = computed(() => props.diagnoseDocSign || props.doctorSign);
  const deptSignUrl = computed(() => props.deptDirectorSign || props.deptSign);

  const orgRef = ref<HTMLImageElement>();

  // const check = () => {
  //   if (props.orgUrl && orgRef.value && orgRef.value.complete) {
  //   }
  // }
</script>

<template>
  <div
    id="cert-view"
    style="
      width: 595.28pt;
      padding: 24pt;
      background-color: #fff;
      position: relative;
      color: black;
      font-size: 12pt;
    "
  >
    <div class="flex justify-center relative">
      <img v-if="orgUrl" :src="orgUrl" class="h-48pt" ref="orgRef" />
    </div>
    <div class="text-24pt text-center font-bold border-b-2 pb-8pt"> 解除隔离医学证明 </div>
    <div class="flex justify-between pb-12pt">
      <div>
        <span>姓名：</span>
        <span class="inline-block min-w-18pt">{{ patientName || name }}</span>
      </div>
      <div>
        <span>性别：</span>
        <span class="inline-block min-w-18pt">{{ sex }}</span>
      </div>
      <div>
        <span>年龄：</span>
        <span class="inline-block min-w-18pt">{{ age }}</span>
        <span>岁</span>
      </div>
      <div>
        <span>科室：</span>
        <span class="inline-block min-w-18pt">{{ deptName }}</span>
      </div>
      <div>
        <span>门诊号：</span>
        <span class="inline-block min-w-90pt">{{ outpatientNo }}</span>
      </div>
    </div>
    <div class="text-12pt leading-24pt">
      兹证明：<br />
      <span class="inline-block min-w-120pt border-b-1 ml-40pt text-center">{{ orgName }}</span>
      学校（单位）
      <span class="inline-block min-w-100pt border-b-1 text-center">{{ departmentName }}</span>
      班（科/部门）学生（职工）
      <span class="inline-block min-w-50pt border-b-1 text-center">{{ patientName || name }}</span>
      联系电话：
      <span class="inline-block min-w-90pt border-b-1 text-center">{{ contactPhone }}</span>
      已于
      <span class="inline-block min-w-120pt border-b-1 text-center">{{ diagnoseDate }}</span>
      在我院被确诊为
      <span class="inline-block min-w-80pt border-b-1 text-center">{{ diagnoseIllness }}</span>
      患者，并于
      <span class="inline-block min-w-150pt border-b-1 text-center">{{ treatDate }}</span>
      开始治疗。经过规范治疗，达到解除隔离标准。特开此证明。
    </div>

    <div class="pt-60pt flex">
      <div>诊断医生（签名）：</div>
      <div class="min-w-150pt flex">
        <img v-if="doctorSignUrl" :src="doctorSignUrl" class="w-60pt" ref="docRef" />
      </div>

      <div>科主任（签名）：</div>
      <div class="min-w-80pt flex relative">
        <img
          v-if="deptSignUrl"
          :style="{ display: isPass ? 'block' : 'none' }"
          :src="deptSignUrl"
          class="w-60pt"
          ref="deptRef"
        />
        <img
          v-if="signature"
          :style="{ display: isPass ? 'block' : 'none' }"
          :src="signature"
          style="width: 180px; left: 80pt; top: -60pt; position: absolute"
          ref="signatureRef"
        />
      </div>
    </div>
    <div :style="{ display: isPass ? 'block' : 'none' }" class="text-right pt-24px">
      {{ issueDateStr }}
    </div>
    <div class="text-right pt-10px">
      <div :style="{ color: 'red' }">证明有效期： {{ effectiveDateStr }}</div>
    </div>
  </div>
</template>
