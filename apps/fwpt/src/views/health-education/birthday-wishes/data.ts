import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 姓名
 * 身份证号
 * 生日（日期范围查询:MM-DD）
 * 联系电话
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'patientName',
    label: '姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'idCardNo',
    label: '身份证号',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'birthday',
    label: '生日',
    component: 'RangePicker',
    componentProps: {
      format: 'MM-DD',
      valueFormat: 'MM-DD',
      placeholder: ['开始日期', '结束日期'],
      style: { width: '100%' },
      getPopupContainer: () => document.body,
    },
    colProps: { span: 6 },
  },
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 姓名
 * 身份证号
 * 性别
 * 年龄
 * 联系电话
 * 地址
 * 生日
 */
export const columns: BasicColumn[] = [
  {
    title: '姓名',
    dataIndex: 'patientName',
    width: 120,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 180,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 80,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 80,
    align: 'left',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 150,
    align: 'left',
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 200,
    align: 'left',
  },
  {
    title: '生日',
    dataIndex: 'birthday',
    width: 120,
    align: 'left',
  },
];

/**
 * 发送记录表格列
 */
export const sendRecordColumns: BasicColumn[] = [
  {
    title: '发送时间',
    dataIndex: 'createTime',
    width: 180,
    align: 'left',
  },
  {
    title: '手机号',
    dataIndex: 'telephone',
    width: 150,
    align: 'left',
  },
  {
    title: '短信内容',
    dataIndex: 'content',
    width: 300,
    align: 'left',
  },
  {
    title: '模板ID',
    dataIndex: 'templateId',
    width: 150,
    align: 'left',
  },
  {
    title: '发送状态',
    dataIndex: 'errorStatus',
    width: 100,
    align: 'left',
    customRender: ({ text }) => {
      return text === 0 ? '成功' : '失败';
    },
  },
  {
    title: '异常内容',
    dataIndex: 'errorContent',
    width: 200,
    align: 'left',
  },
];
