<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { message } from 'ant-design-vue';
  import { columns, searchFormSchema } from './data';
  import SendRecordModal from './SendRecordModal.vue';
  import { birthdayWishesSend, getBirthdayWishesPatientList } from '/@/api/birthday-wishes';

  const [registerTable, tableIns] = useTable({
    api: getBirthdayWishesPatientList,
    columns,
    useSearchForm: true,
    formConfig: {
      colon: true,
      labelWidth: 100,
      schemas: searchFormSchema,
      fieldMapToTime: [['birthday', ['birthdayStart', 'birthdayEnd'], 'MM-DD']],
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
    resizeHeightOffset: 10,
  });

  const [registerModal, { openModal }] = useModal();

  // 短信发送
  const { runAsync: sendSmsRunAsync, loading: sendSmsLoading } = useRequest(birthdayWishesSend, {
    manual: true,
    onSuccess() {
      message.success('短信发送成功');
      tableIns.reload();
    },
    onError(error) {
      message.error(`短信发送失败：${error.message || '未知错误'}`);
    },
  });

  function onSendSms(record: any) {
    if (!record.phone) {
      message.warning('该患者没有联系电话，无法发送短信');
      return;
    }
    sendSmsRunAsync(record.phone);
  }

  function onViewSendRecord(record: any) {
    if (!record.phone) {
      message.warning('该患者没有联系电话，无法查看发送记录');
      return;
    }
    openModal(true, record);
  }

  function createActions(record: Recordable): ActionItem[] {
    return [
      {
        label: '短信发送',
        type: 'link',
        loading: sendSmsLoading.value,
        onClick: onSendSms.bind(null, record),
      },
      {
        label: '发送记录',
        type: 'link',
        onClick: onViewSendRecord.bind(null, record),
      },
    ];
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">生日祝福列表</span>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <SendRecordModal @register="registerModal" />
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
