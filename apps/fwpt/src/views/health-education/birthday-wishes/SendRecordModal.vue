<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { ref } from 'vue';
  import { sendRecordColumns } from './data';
  import { getBirthdayWishesSendRecord } from '/@/api/birthday-wishes';

  defineEmits(['register']);

  const patientInfo = ref<any>({});

  const [registerTable, tableIns] = useTable({
    api: async () => {
      if (!patientInfo.value.phone) return [];
      const result = await getBirthdayWishesSendRecord(patientInfo.value.phone);
      return result || [];
    },
    columns: sendRecordColumns,
    useSearchForm: false,
    showIndexColumn: true,
    bordered: true,
    size: 'small',
    pagination: false,
  });

  const [register] = useModalInner((data) => {
    patientInfo.value = data;
    // 重新加载表格数据
    tableIns.reload();
  });
</script>

<template>
  <BasicModal
    @register="register"
    :width="1000"
    :title="`${patientInfo.patientName} - 发送记录`"
    :show-ok-btn="false"
    :show-cancel-btn="false"
    :min-height="400"
  >
    <div class="p-4">
      <div class="mb-4 flex justify-between">
        <p><strong>患者姓名：</strong>{{ patientInfo.patientName }}</p>
        <p><strong>联系电话：</strong>{{ patientInfo.phone }}</p>
        <p><strong>身份证号：</strong>{{ patientInfo.idCardNo }}</p>
      </div>
      <BasicTable @register="registerTable" />
    </div>
  </BasicModal>
</template>

<style scoped>
  :deep(.vben-basic-table .ant-table-wrapper) {
    padding: 0;
  }
</style>
