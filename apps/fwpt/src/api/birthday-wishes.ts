import { defHttp } from '@ft/request';

interface BirthdayWishesPatientListParams {
  /** 生日_结束日期 mm-dd */
  birthdayEnd?: string;
  /** 生日_开始日期 mm-dd */
  birthdayStart?: string;
  /** 身份证号 */
  idCardNo?: string;
  /** 患者姓名 */
  patientName?: string;
  /** 手机号 */
  phone?: string;
}

export interface BirthdayWishesPatient {
  /** 现住址 */
  address?: string;
  /** 年龄 */
  age?: string;
  /** 现住址 */
  birthday?: string;
  /** 身份证号 */
  idCardNo?: string;
  /** 患者姓名 */
  patientName?: string;
  /** 手机号 */
  phone?: string;
  /** 性别 */
  sex?: string;
}

/**
 * 生日祝福-短信发送患者列表
 * POST /educationPushRecord/birthdayWishesPatientList
 */
export function getBirthdayWishesPatientList(data: BirthdayWishesPatientListParams) {
  return defHttp.post<BirthdayWishesPatient[]>({
    url: '/educationPushRecord/birthdayWishesPatientList',
    data,
  });
}

/**
 * 生日祝福-短信发送
 * GET /educationPushRecord/birthdayWishesSend
 */
export function birthdayWishesSend(phone: string) {
  return defHttp.get({
    url: '/educationPushRecord/birthdayWishesSend',
    params: {
      phone,
    },
  });
}

export interface BirthdayWishesSendRecord {
  /** 短信内容 */
  content?: string;
  /** 发送时间 */
  createTime?: string;
  /** 异常内容 */
  errorContent?: string;
  /** 错误标识 0:否 1:是 */
  errorStatus?: number;
  /** 手机号 */
  telephone?: string;
  /** 模板ID */
  templateId?: string;
}

/**
 * 生日祝福-短信发送记录查询
 * GET /educationPushRecord/queryBirthdayWishesSendRecord
 */
export function getBirthdayWishesSendRecord(phone: string) {
  return defHttp.get<BirthdayWishesSendRecord[]>({
    url: '/educationPushRecord/queryBirthdayWishesSendRecord',
    params: {
      phone,
    },
  });
}
